body {
    font-family: 'Arial', sans-serif;
    color: #fff;
    min-height: 100vh;
    overflow-x: hidden;
}

.space-card {
    background-color: rgba(25, 32, 71, 0.8);
    border: 1px solid rgba(83, 109, 254, 0.5);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(83, 109, 254, 0.3);
    backdrop-filter: blur(10px);
}

.space-card-header {
    background-color: rgba(37, 45, 90, 0.8);
    border-bottom: 1px solid rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-title {
    color: #fff;
    text-shadow: 0 0 10px rgba(83, 109, 254, 0.8);
    font-weight: bold;
}

.space-input {
    background-color: rgba(25, 32, 71, 0.6);
    border: 1px solid rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-input:focus {
    background-color: rgba(37, 45, 90, 0.8);
    border-color: rgba(83, 109, 254, 0.8);
    box-shadow: 0 0 0 0.25rem rgba(83, 109, 254, 0.25);
    color: #fff;
}

.space-btn {
    border-color: rgba(83, 109, 254, 0.5);
    color: rgba(83, 109, 254, 1);
}

.space-btn:hover {
    background-color: rgba(83, 109, 254, 0.2);
    border-color: rgba(83, 109, 254, 0.8);
    color: #fff;
}

.space-btn-primary {
    background-color: rgba(83, 109, 254, 0.8);
    border-color: rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-btn-primary:hover {
    background-color: rgba(83, 109, 254, 1);
    border-color: rgba(83, 109, 254, 0.8);
}

.space-badge {
    background-color: rgba(37, 45, 90, 0.8);
    border: 1px solid rgba(83, 109, 254, 0.3);
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
    color: #3498db !important;
    font-weight: bold;
}

.typing-container {
    position: relative;
}

.text-display {
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 5px;
    padding: 20px;
    font-size: 1.5rem;
    line-height: 1.8;
    height: 200px;  /* 固定高度 */
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
    position: relative;
    overflow: hidden;  /* 防止内容溢出 */
}

.text-display .correct {
    color: #28a745;
}

.text-display .incorrect {
    color: #ff4444;
}

.text-display .current {
    background-color: rgba(83, 109, 254, 0.3);
    border-radius: 2px;
}

.text-display small {
    font-size: 0.9rem;
    display: block;
    margin-top: 10px;
    color: #adb5bd;
}

.input-container {
    margin-top: 20px;
}

.typing-container .input-container {
    margin-top: 20px;
}

.typing-container .space-input {
    height: 100px;  /* 减小输入框高度 */
    font-size: 1.5rem;
    line-height: 1.8;
    padding: 15px;
    resize: none;
}

.status-message {
    font-size: 0.9rem;
    color: #adb5bd;
    text-align: center;
}

.rocket-animation {
    animation: rocket-float 3s ease-in-out infinite;
}

.rocket {
    width: 100px;
    height: auto;
}

@keyframes rocket-float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

.medal {
    width: 120px;
    height: auto;
    animation: medal-shine 2s ease-in-out infinite;
}

@keyframes medal-shine {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.3);
    }
    100% {
        filter: brightness(1);
    }
}

.waiting-text {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.user-count {
    font-size: 1rem;
    color: #adb5bd;
}

table.table-dark {
    background-color: transparent;
}

table.table-dark thead th {
    background-color: rgba(37, 45, 90, 0.8);
    border-color: rgba(83, 109, 254, 0.3);
}

table.table-dark tbody td {
    background-color: rgba(25, 32, 71, 0.5);
    border-color: rgba(83, 109, 254, 0.2);
}

table.table-dark tbody tr:hover td {
    background-color: rgba(37, 45, 90, 0.7);
}

/* 昵称显示样式 */
#nickname {
    color: #3498db;
    font-weight: bold;
}

/* 添加结果界面的样式 */
.result-stats {
    font-size: 1.5rem;
    margin: 20px 0;
    padding: 20px;
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 10px;
}

.result-stats p {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;
}

.result-stats span {
    font-weight: bold;
    color: #3498db;
}

/* 修改文本显示区域宽度 */
.col-md-8 {
    width: 70%;  /* 增加主要内容区域的宽度 */
}

.col-md-4 {
    width: 30%;  /* 减小排行榜区域的宽度 */
}

.current-line {
    background-color: rgba(83, 109, 254, 0.3);  /* 增加背景色透明度 */
    padding: 8px;  /* 增加内边距 */
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #3498db;  /* 添加左边框 */
    box-shadow: 0 0 5px rgba(83, 109, 254, 0.5);  /* 添加发光效果 */
}

.other-line {
    color: #95a5a6;  /* 调整其他行的颜色 */
    margin-bottom: 10px;
    padding: 8px;
    opacity: 0.7;  /* 降低其他行的不透明度 */
}

.page-info {
    position: absolute;
    bottom: 10px;
    right: 20px;
    font-size: 0.8rem;
    color: #95a5a6;
}

/* 修改结果界面的样式 */
#result-screen .card-body {
    padding: 30px;
}

#result-screen .result-stats {
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

#result-screen .result-stats p {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    font-size: 1.2rem;
}

#result-screen .result-stats span {
    color: #3498db;
    font-weight: bold;
}

#result-screen .medal {
    width: 120px;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* 结果界面的排行榜样式 */
#result-screen .table {
    margin-bottom: 0;
}

#result-screen .table th,
#result-screen .table td {
    padding: 12px;
    text-align: center;
}

/* 添加昵称显示样式 */
.user-nickname-display {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4dabf7;
    text-shadow: 0 0 10px rgba(77, 171, 247, 0.5);
    background-color: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 10px;
    border: 1px solid rgba(77, 171, 247, 0.3);
    display: inline-block;
    margin-bottom: 15px;
}

/* 排行榜中的昵称样式 */
.leaderboard-nickname {
    font-weight: 600;
    color: #4dabf7;
}

/* 排行榜前三名特殊样式 */
.top-three {
    position: relative;
    font-weight: bold;
    border-radius: 8px;
    margin: 2px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.top-three:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 第一名 - 金色渐变 */
.rank-1 {
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.3) 0%,
        rgba(255, 193, 7, 0.2) 50%,
        rgba(255, 235, 59, 0.3) 100%);
    border: 2px solid rgba(255, 215, 0, 0.6);
    color: #fff;
}

.rank-1 .leaderboard-nickname {
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

/* 第二名 - 银色渐变 */
.rank-2 {
    background: linear-gradient(135deg,
        rgba(192, 192, 192, 0.3) 0%,
        rgba(169, 169, 169, 0.2) 50%,
        rgba(211, 211, 211, 0.3) 100%);
    border: 2px solid rgba(192, 192, 192, 0.6);
    color: #fff;
}

.rank-2 .leaderboard-nickname {
    color: #C0C0C0;
    text-shadow: 0 0 10px rgba(192, 192, 192, 0.8);
}

/* 第三名 - 铜色渐变 */
.rank-3 {
    background: linear-gradient(135deg,
        rgba(205, 127, 50, 0.3) 0%,
        rgba(184, 115, 51, 0.2) 50%,
        rgba(218, 165, 32, 0.3) 100%);
    border: 2px solid rgba(205, 127, 50, 0.6);
    color: #fff;
}

.rank-3 .leaderboard-nickname {
    color: #CD7F32;
    text-shadow: 0 0 10px rgba(205, 127, 50, 0.8);
}

/* 排行榜前三名特殊样式 */
.top-three {
    position: relative;
    font-weight: bold;
    border-radius: 8px;
    margin: 2px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.top-three:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

/* 第一名 - 金色渐变 */
.rank-1 {
    background: linear-gradient(135deg,
        rgba(255, 215, 0, 0.3) 0%,
        rgba(255, 193, 7, 0.2) 50%,
        rgba(255, 235, 59, 0.3) 100%);
    border: 2px solid rgba(255, 215, 0, 0.6);
    color: #fff;
}

.rank-1 .leaderboard-nickname {
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
}

/* 第二名 - 银色渐变 */
.rank-2 {
    background: linear-gradient(135deg,
        rgba(192, 192, 192, 0.3) 0%,
        rgba(169, 169, 169, 0.2) 50%,
        rgba(211, 211, 211, 0.3) 100%);
    border: 2px solid rgba(192, 192, 192, 0.6);
    color: #fff;
}

.rank-2 .leaderboard-nickname {
    color: #C0C0C0;
    text-shadow: 0 0 10px rgba(192, 192, 192, 0.8);
}

/* 第三名 - 铜色渐变 */
.rank-3 {
    background: linear-gradient(135deg,
        rgba(205, 127, 50, 0.3) 0%,
        rgba(184, 115, 51, 0.2) 50%,
        rgba(218, 165, 32, 0.3) 100%);
    border: 2px solid rgba(205, 127, 50, 0.6);
    color: #fff;
}

.rank-3 .leaderboard-nickname {
    color: #CD7F32;
    text-shadow: 0 0 10px rgba(205, 127, 50, 0.8);
}

/* 排名徽章样式 */
.rank-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 5px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(5px);
    min-width: 60px;
}

.medal-icon {
    font-size: 1.2em;
    filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
}

.rank-number {
    font-weight: bold;
    font-size: 0.9em;
}

/* 金牌徽章特效 */
.rank-badge.rank-1 {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
    animation: goldGlow 2s ease-in-out infinite alternate;
}

.rank-badge.rank-1 .rank-number {
    color: #8B4513;
}

/* 银牌徽章特效 */
.rank-badge.rank-2 {
    background: linear-gradient(45deg, #C0C0C0, #A9A9A9);
    box-shadow: 0 0 15px rgba(192, 192, 192, 0.5);
    animation: silverGlow 2s ease-in-out infinite alternate;
}

.rank-badge.rank-2 .rank-number {
    color: #2F4F4F;
}

/* 铜牌徽章特效 */
.rank-badge.rank-3 {
    background: linear-gradient(45deg, #CD7F32, #B8860B);
    box-shadow: 0 0 15px rgba(205, 127, 50, 0.5);
    animation: bronzeGlow 2s ease-in-out infinite alternate;
}

.rank-badge.rank-3 .rank-number {
    color: #8B4513;
}

/* 完成后的消息样式 */
.completed-message {
    color: #28a745;
    font-size: 1.5rem;
    text-align: center;
    padding: 20px;
    margin-top: 20px;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.waiting-message {
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
}

.waiting-message .spinner-border {
    margin-left: 10px;
} 